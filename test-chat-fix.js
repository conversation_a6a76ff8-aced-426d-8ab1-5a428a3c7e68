/**
 * Simple test to verify the chat repetition fix is working
 * This script tests the database-backed conversation storage
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3000';
const TEST_SESSION_ID = `test_fix_${Date.now()}`;

// Fresh JWT token provided by user
const JWT_TOKEN = 'eyJhbGciOiJIUzI1NiIsImtpZCI6Ikt3WTZnd1BhVksxZTJqVjUiLCJ0eXAiOiJKV1QifQ.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.5XXP2aUXHf85V3RMdZgTSa7tQKRg4l6dg5lQOpg2GeY';

async function testChatFix() {
  console.log('🧪 Testing Chat Repetition Fix...\n');
  
  if (JWT_TOKEN === 'YOUR_FRESH_JWT_TOKEN_HERE') {
    console.log('❌ Please update the JWT_TOKEN in this script with a fresh token');
    console.log('   You can get one by logging into your application');
    return;
  }
  
  try {
    console.log('📝 Test 1: Sending message with context...');
    const response1 = await axios.post(
      `${BASE_URL}/chat`,
      {
        message: 'My name is John and I work in education. Please remember this.',
        session_id: TEST_SESSION_ID
      },
      {
        headers: {
          'Authorization': `Bearer ${JWT_TOKEN}`,
          'Content-Type': 'application/json'
        }
      }
    );
    
    console.log(`✅ Response 1: "${response1.data.response.substring(0, 100)}..."`);
    console.log(`📋 Session ID: ${response1.data.session_id}`);
    console.log(`🔧 LLM Provider: ${response1.data.metadata?.llm_provider || 'Unknown'}`);
    
    // Check if it's a fallback response
    if (response1.data.metadata?.llm_provider === 'FALLBACK') {
      console.log('⚠️  WARNING: Received fallback response - there may be an issue');
    }
    
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    console.log('\n📝 Test 2: Testing conversation memory...');
    const response2 = await axios.post(
      `${BASE_URL}/chat`,
      {
        message: 'What is my name and what field do I work in?',
        session_id: TEST_SESSION_ID
      },
      {
        headers: {
          'Authorization': `Bearer ${JWT_TOKEN}`,
          'Content-Type': 'application/json'
        }
      }
    );
    
    console.log(`✅ Response 2: "${response2.data.response.substring(0, 200)}..."`);
    console.log(`🔧 LLM Provider: ${response2.data.metadata?.llm_provider || 'Unknown'}`);
    
    // Analyze the response
    const response = response2.data.response.toLowerCase();
    const remembersName = response.includes('john');
    const remembersField = response.includes('education');
    const isGenericResponse = response.includes('trouble accessing') || response.includes('start fresh');
    const isFallback = response2.data.metadata?.llm_provider === 'FALLBACK';
    
    console.log('\n📊 Memory Test Results:');
    console.log(`   Remembers name (John): ${remembersName ? '✅' : '❌'}`);
    console.log(`   Remembers field (education): ${remembersField ? '✅' : '❌'}`);
    console.log(`   Generic/fallback response: ${isGenericResponse ? '❌' : '✅'}`);
    console.log(`   Using real LLM: ${!isFallback ? '✅' : '❌'}`);
    
    if (isFallback) {
      console.log('\n❌ ISSUE: Application is still using fallback responses');
      console.log('   This suggests there may be an error in the chat service');
      console.log('   Check application logs for errors');
    } else if (isGenericResponse) {
      console.log('\n❌ ISSUE: Application is giving generic fallback responses');
      console.log('   The DatabaseConversationStateService may not be working properly');
    } else if (remembersName && remembersField) {
      console.log('\n🎉 SUCCESS: Conversation memory is working!');
      console.log('   The chat repetition fix has been successfully implemented');
    } else {
      console.log('\n⚠️  PARTIAL SUCCESS: Some memory issues detected');
      console.log('   The conversation context may not be fully preserved');
    }
    
    console.log('\n🔍 Test 3: Testing conversation history endpoint...');
    try {
      const historyResponse = await axios.get(
        `${BASE_URL}/chat/${TEST_SESSION_ID}/history`,
        {
          headers: {
            'Authorization': `Bearer ${JWT_TOKEN}`
          }
        }
      );
      
      console.log(`✅ History retrieved: ${historyResponse.data.messages.length} messages`);
      console.log(`📋 Total messages: ${historyResponse.data.total_messages}`);
      
    } catch (historyError) {
      console.log(`❌ History endpoint failed: ${historyError.response?.status} - ${historyError.response?.data?.message || historyError.message}`);
    }
    
    console.log('\n✅ Test completed!');
    
  } catch (error) {
    console.error('\n❌ Test failed:', error.response?.data || error.message);
    
    if (error.response?.status === 401) {
      console.log('\n💡 TIP: Your JWT token may have expired. Get a fresh one from your application.');
    }
  }
}

// Instructions for getting a fresh JWT token
console.log('🔑 To get a fresh JWT token:');
console.log('1. Open your application in a browser');
console.log('2. Open Developer Tools (F12)');
console.log('3. Go to Application/Storage > Local Storage');
console.log('4. Look for your auth token');
console.log('5. Copy the token and replace JWT_TOKEN in this script');
console.log('');

// Run the test
testChatFix();
