import { Injectable, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { PrismaClient } from '../../generated/prisma';

@Injectable()
export class PrismaService extends PrismaClient
  implements OnModuleInit, OnModuleDestroy {
  constructor() {
    super({
      log: [
        { emit: 'stdout', level: 'query' }, // Enable query logging
        { emit: 'stdout', level: 'info' },
        { emit: 'stdout', level: 'warn' },
        { emit: 'stdout', level: 'error' },
      ],
      // Optional: Configure logging, datasources, etc.
      // log: ['query', 'info', 'warn', 'error'],
      // datasources: {
      //   db: {
      //     url: process.env.DATABASE_URL, // Explicitly use DATABASE_URL from env
      //   },
      // },
    });
  }

  async onModuleInit() {
    try {
      await this.$connect();
      console.log('Prisma Client connected successfully to the database.');
    } catch (error) {
      console.error('Failed to connect to the database:', error);
      // Optional: Implement retry logic or throw an error to halt application startup
    }
  }

  async onModuleDestroy() {
    await this.$disconnect();
    console.log('Prisma Client disconnected from the database.');
  }

  // Add custom methods here if needed, e.g., for clean shutdown
  // async enableShutdownHooks(app: INestApplication) {
  //   this.$on('beforeExit', async () => {
  //     await app.close();
  //   });
  // }
} 