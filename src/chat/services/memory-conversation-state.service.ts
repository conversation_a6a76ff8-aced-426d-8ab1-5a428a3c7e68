import { Injectable, Logger, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { LRUCache } from 'lru-cache';
import {
  IConversationStateService,
  ConversationStateConfig,
  ConversationSession,
} from '../interfaces/conversation-state.interface';
import { ConversationContext } from '../../common/llm/interfaces/llm.service.interface';

/**
 * In-memory implementation of conversation state service using LRU cache
 * Suitable for single-instance deployments or development
 * For production with multiple instances, consider Redis implementation
 */
@Injectable()
export class MemoryConversationStateService
  implements IConversationStateService, OnModuleInit, OnModuleDestroy
{
  private readonly logger = new Logger(MemoryConversationStateService.name);
  private readonly cache: LRUCache<string, ConversationSession>;
  private readonly userSessionsIndex: Map<string, Set<string>> = new Map();
  private cleanupInterval: NodeJS.Timeout | null = null;
  private readonly config: ConversationStateConfig;

  constructor(private readonly configService: ConfigService) {
    // Load configuration with defaults
    this.config = {
      defaultTtlSeconds: this.configService.get<number>('CHAT_SESSION_TTL_SECONDS', 3600), // 1 hour
      maxSessions: this.configService.get<number>('CHAT_MAX_SESSIONS', 1000),
      cleanupIntervalMs: this.configService.get<number>('CHAT_CLEANUP_INTERVAL_MS', 300000), // 5 minutes
      enableMetrics: this.configService.get<boolean>('CHAT_ENABLE_METRICS', true),
    };

    // Initialize LRU cache
    this.cache = new LRUCache<string, ConversationSession>({
      max: this.config.maxSessions,
      ttl: this.config.defaultTtlSeconds * 1000, // Convert to milliseconds
      updateAgeOnGet: true,
      updateAgeOnHas: true,
      dispose: (session, sessionId) => {
        this.removeFromUserIndex(session.userId, sessionId);
        this.logger.debug(`Session ${sessionId} disposed from cache`);
      },
    });

    this.logger.log(
      `Initialized memory conversation state service with max ${this.config.maxSessions} sessions, TTL ${this.config.defaultTtlSeconds}s`,
    );
  }

  onModuleInit() {
    // Start cleanup interval
    if (this.config.cleanupIntervalMs > 0) {
      this.cleanupInterval = setInterval(() => {
        this.cleanupExpiredConversations().catch((error) => {
          this.logger.error('Error during cleanup', error.stack);
        });
      }, this.config.cleanupIntervalMs);

      this.logger.log(`Started cleanup interval: ${this.config.cleanupIntervalMs}ms`);
    }
  }

  onModuleDestroy() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
    this.cache.clear();
    this.userSessionsIndex.clear();
    this.logger.log('Memory conversation state service destroyed');
  }

  async setConversationContext(
    sessionId: string,
    context: ConversationContext,
    ttlSeconds?: number,
  ): Promise<void> {
    const now = new Date();
    const ttl = ttlSeconds || this.config.defaultTtlSeconds;
    const expiresAt = new Date(now.getTime() + ttl * 1000);

    const existingSession = this.cache.get(sessionId);
    
    const session: ConversationSession = {
      sessionId,
      userId: context.userId,
      context: {
        ...context,
        metadata: {
          ...context.metadata,
          lastActiveAt: now,
        },
      },
      createdAt: existingSession?.createdAt || now,
      lastAccessedAt: now,
      expiresAt,
    };

    // Update cache with custom TTL if provided
    if (ttlSeconds) {
      this.cache.set(sessionId, session, { ttl: ttlSeconds * 1000 });
    } else {
      this.cache.set(sessionId, session);
    }

    // Update user sessions index
    this.addToUserIndex(context.userId, sessionId);

    this.logger.debug(
      `Stored conversation context for session ${sessionId}, user ${context.userId}, expires at ${expiresAt.toISOString()}`,
    );
  }

  async getConversationContext(sessionId: string): Promise<ConversationContext | null> {
    const session = this.cache.get(sessionId);

    if (!session) {
      this.logger.debug(`No conversation context found for session ${sessionId}`);
      return null;
    }

    this.logger.debug(`Retrieved session ${sessionId} with ${session.context.messages?.length || 0} messages`);

    // Update last accessed time
    session.lastAccessedAt = new Date();
    this.cache.set(sessionId, session);

    this.logger.debug(`Retrieved conversation context for session ${sessionId}`);
    return session.context;
  }

  async deleteConversationContext(sessionId: string): Promise<void> {
    const session = this.cache.get(sessionId);
    
    if (session) {
      this.removeFromUserIndex(session.userId, sessionId);
    }

    this.cache.delete(sessionId);
    this.logger.debug(`Deleted conversation context for session ${sessionId}`);
  }

  async hasConversationContext(sessionId: string): Promise<boolean> {
    return this.cache.has(sessionId);
  }

  async getUserActiveSessions(userId: string): Promise<string[]> {
    const userSessions = this.userSessionsIndex.get(userId);
    
    if (!userSessions) {
      return [];
    }

    // Filter out expired sessions
    const activeSessions: string[] = [];
    for (const sessionId of userSessions) {
      if (this.cache.has(sessionId)) {
        activeSessions.push(sessionId);
      }
    }

    // Update index to remove expired sessions
    if (activeSessions.length !== userSessions.size) {
      this.userSessionsIndex.set(userId, new Set(activeSessions));
    }

    return activeSessions;
  }

  async cleanupExpiredConversations(): Promise<number> {
    const initialSize = this.cache.size;
    
    // LRU cache automatically handles TTL-based expiration
    // We just need to clean up the user sessions index
    for (const [userId, sessionIds] of this.userSessionsIndex.entries()) {
      const activeSessionIds = new Set<string>();
      
      for (const sessionId of sessionIds) {
        if (this.cache.has(sessionId)) {
          activeSessionIds.add(sessionId);
        }
      }

      if (activeSessionIds.size === 0) {
        this.userSessionsIndex.delete(userId);
      } else if (activeSessionIds.size !== sessionIds.size) {
        this.userSessionsIndex.set(userId, activeSessionIds);
      }
    }

    const finalSize = this.cache.size;
    const cleanedUp = initialSize - finalSize;

    if (cleanedUp > 0) {
      this.logger.log(`Cleaned up ${cleanedUp} expired conversation sessions`);
    }

    return cleanedUp;
  }

  async getStats(): Promise<{
    totalSessions: number;
    activeSessions: number;
    memoryUsage?: number;
  }> {
    const stats = {
      totalSessions: this.cache.size,
      activeSessions: this.cache.size, // All cached sessions are considered active
      memoryUsage: this.config.enableMetrics ? this.estimateMemoryUsage() : undefined,
    };

    return stats;
  }

  private addToUserIndex(userId: string, sessionId: string): void {
    if (!this.userSessionsIndex.has(userId)) {
      this.userSessionsIndex.set(userId, new Set());
    }
    this.userSessionsIndex.get(userId)!.add(sessionId);
  }

  private removeFromUserIndex(userId: string, sessionId: string): void {
    const userSessions = this.userSessionsIndex.get(userId);
    if (userSessions) {
      userSessions.delete(sessionId);
      if (userSessions.size === 0) {
        this.userSessionsIndex.delete(userId);
      }
    }
  }

  private estimateMemoryUsage(): number {
    // Rough estimation of memory usage in bytes
    let totalSize = 0;
    
    for (const [sessionId, session] of this.cache.entries()) {
      // Estimate size of session object
      totalSize += sessionId.length * 2; // UTF-16 characters
      totalSize += JSON.stringify(session).length * 2; // Rough estimate
    }

    return totalSize;
  }
}
