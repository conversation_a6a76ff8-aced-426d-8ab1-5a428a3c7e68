{"version": 3, "file": "conversation-manager.service.js", "sourceRoot": "", "sources": ["../../../src/chat/services/conversation-manager.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA,2CAA4D;AAC5D,+BAAoC;AAa7B,IAAM,0BAA0B,kCAAhC,MAAM,0BAA0B;IAGrC,YAEE,wBAAoE;QAAnD,6BAAwB,GAAxB,wBAAwB,CAA2B;QAJrD,WAAM,GAAG,IAAI,eAAM,CAAC,4BAA0B,CAAC,IAAI,CAAC,CAAC;IAKnE,CAAC;IAKJ,KAAK,CAAC,sBAAsB,CAC1B,MAAc,EACd,SAAkB;QAElB,MAAM,cAAc,GAAG,SAAS,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAE7D,MAAM,OAAO,GAAwB;YACnC,SAAS,EAAE,cAAc;YACzB,MAAM;YACN,QAAQ,EAAE,EAAE;YACZ,kBAAkB,EAAE,EAAE;YACtB,eAAe,EAAE,EAAE;YACnB,iBAAiB,EAAE,UAAU;YAC7B,QAAQ,EAAE;gBACR,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,YAAY,EAAE,IAAI,IAAI,EAAE;gBACxB,aAAa,EAAE,CAAC;gBAChB,aAAa,EAAE,EAAE;aAClB;SACF,CAAC;QAEF,MAAM,IAAI,CAAC,wBAAwB,CAAC,sBAAsB,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;QAEpF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,wCAAwC,cAAc,aAAa,MAAM,EAAE,CAAC,CAAC;QAC7F,OAAO,OAAO,CAAC;IACjB,CAAC;IAKD,KAAK,CAAC,uBAAuB,CAC3B,MAAc,EACd,SAAkB;QAElB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,qEAAqE,MAAM,eAAe,SAAS,EAAE,CAAC,CAAC;QAEvH,IAAI,SAAS,EAAE,CAAC;YACd,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,uDAAuD,SAAS,EAAE,CAAC,CAAC;YACpF,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,sBAAsB,CAAC,SAAS,CAAC,CAAC;YAE9F,IAAI,eAAe,EAAE,CAAC;gBAEpB,IAAI,eAAe,CAAC,MAAM,KAAK,MAAM,EAAE,CAAC;oBACtC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,SAAS,mDAAmD,CAAC,CAAC;oBAC1F,OAAO,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC;gBAC7C,CAAC;gBAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2CAA2C,SAAS,EAAE,CAAC,CAAC;gBAC1E,OAAO,eAAe,CAAC;YACzB,CAAC;QACH,CAAC;QAGD,OAAO,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;IACxD,CAAC;IAKD,KAAK,CAAC,UAAU,CACd,SAAiB,EACjB,OAA8C,EAC9C,MAAmB;QAEnB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,sBAAsB,CAAC,SAAS,CAAC,CAAC;QAEtF,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,wBAAwB,SAAS,YAAY,CAAC,CAAC;QACjE,CAAC;QAED,MAAM,WAAW,GAAgB;YAC/B,EAAE,EAAE,IAAA,SAAM,GAAE;YACZ,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,GAAG,OAAO;SACX,CAAC;QAGF,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAGnC,OAAO,CAAC,QAAQ,CAAC,YAAY,GAAG,IAAI,IAAI,EAAE,CAAC;QAC3C,OAAO,CAAC,QAAQ,CAAC,aAAa,GAAG,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC;QAGzD,IAAI,MAAM,EAAE,CAAC;YACX,OAAO,CAAC,aAAa,GAAG,MAAM,CAAC;QACjC,CAAC;QAGD,IAAI,CAAC,2BAA2B,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QAGlD,MAAM,IAAI,CAAC,wBAAwB,CAAC,sBAAsB,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QAE/E,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,SAAS,OAAO,CAAC,IAAI,uBAAuB,SAAS,qBAAqB,OAAO,CAAC,QAAQ,CAAC,MAAM,EAAE,CACpG,CAAC;QAEF,OAAO,OAAO,CAAC;IACjB,CAAC;IAKD,KAAK,CAAC,qBAAqB,CACzB,SAAiB,EACjB,WAA4D;QAE5D,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,sBAAsB,CAAC,SAAS,CAAC,CAAC;QAEtF,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,wBAAwB,SAAS,YAAY,CAAC,CAAC;QACjE,CAAC;QAGD,OAAO,CAAC,eAAe,GAAG;YACxB,GAAG,OAAO,CAAC,eAAe;YAC1B,GAAG,WAAW;SACf,CAAC;QAEF,OAAO,CAAC,QAAQ,CAAC,YAAY,GAAG,IAAI,IAAI,EAAE,CAAC;QAE3C,MAAM,IAAI,CAAC,wBAAwB,CAAC,sBAAsB,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QAE/E,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wCAAwC,SAAS,EAAE,CAAC,CAAC;QACvE,OAAO,OAAO,CAAC;IACjB,CAAC;IAKD,KAAK,CAAC,qBAAqB,CACzB,SAAiB,EACjB,SAAmB;QAEnB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,sBAAsB,CAAC,SAAS,CAAC,CAAC;QAEtF,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,wBAAwB,SAAS,YAAY,CAAC,CAAC;QACjE,CAAC;QAGD,MAAM,WAAW,GAAG,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,kBAAkB,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;QACrF,OAAO,CAAC,kBAAkB,CAAC,IAAI,CAAC,GAAG,WAAW,CAAC,CAAC;QAGhD,OAAO,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,WAAW,CAAC,CAAC;QACpD,OAAO,CAAC,QAAQ,CAAC,YAAY,GAAG,IAAI,IAAI,EAAE,CAAC;QAE3C,MAAM,IAAI,CAAC,wBAAwB,CAAC,sBAAsB,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QAE/E,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,SAAS,WAAW,CAAC,MAAM,4BAA4B,SAAS,YAAY,OAAO,CAAC,kBAAkB,CAAC,MAAM,EAAE,CAChH,CAAC;QAEF,OAAO,OAAO,CAAC;IACjB,CAAC;IAKD,KAAK,CAAC,uBAAuB,CAC3B,SAAiB,EACjB,KAA+C;QAE/C,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,sBAAsB,CAAC,SAAS,CAAC,CAAC;QAEtF,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,wBAAwB,SAAS,YAAY,CAAC,CAAC;QACjE,CAAC;QAED,MAAM,aAAa,GAAG,OAAO,CAAC,iBAAiB,CAAC;QAChD,OAAO,CAAC,iBAAiB,GAAG,KAAK,CAAC;QAClC,OAAO,CAAC,QAAQ,CAAC,YAAY,GAAG,IAAI,IAAI,EAAE,CAAC;QAE3C,MAAM,IAAI,CAAC,wBAAwB,CAAC,sBAAsB,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QAE/E,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,0CAA0C,SAAS,KAAK,aAAa,OAAO,KAAK,EAAE,CACpF,CAAC;QAEF,OAAO,OAAO,CAAC;IACjB,CAAC;IAKD,KAAK,CAAC,sBAAsB,CAC1B,SAAiB,EACjB,QAAgB,EAAE;QAElB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,sBAAsB,CAAC,SAAS,CAAC,CAAC;QAEtF,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,OAAO,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC;IACxC,CAAC;IAKD,KAAK,CAAC,eAAe,CAAC,SAAiB;QACrC,MAAM,IAAI,CAAC,wBAAwB,CAAC,yBAAyB,CAAC,SAAS,CAAC,CAAC;QACzE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,8BAA8B,SAAS,EAAE,CAAC,CAAC;IAC7D,CAAC;IAKD,KAAK,CAAC,qBAAqB,CAAC,MAAc;QACxC,OAAO,IAAI,CAAC,wBAAwB,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;IACrE,CAAC;IAKO,iBAAiB;QACvB,OAAO,QAAQ,IAAA,SAAM,GAAE,EAAE,CAAC;IAC5B,CAAC;IAKO,2BAA2B,CACjC,OAA4B,EAC5B,MAAmB;QAEnB,MAAM,YAAY,GAAG,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC;QAC7C,MAAM,YAAY,GAAG,OAAO,CAAC,iBAAiB,CAAC;QAG/C,IAAI,YAAY,KAAK,UAAU,IAAI,YAAY,IAAI,CAAC,EAAE,CAAC;YACrD,OAAO,CAAC,iBAAiB,GAAG,WAAW,CAAC;QAC1C,CAAC;aAAM,IAAI,YAAY,KAAK,WAAW,IAAI,MAAM,EAAE,IAAI,KAAK,YAAY,EAAE,CAAC;YACzE,OAAO,CAAC,iBAAiB,GAAG,YAAY,CAAC;QAC3C,CAAC;aAAM,IACL,CAAC,YAAY,KAAK,WAAW,IAAI,YAAY,KAAK,YAAY,CAAC;YAC/D,OAAO,CAAC,kBAAkB,CAAC,MAAM,IAAI,CAAC,EACtC,CAAC;YACD,OAAO,CAAC,iBAAiB,GAAG,gBAAgB,CAAC;QAC/C,CAAC;aAAM,IAAI,MAAM,EAAE,IAAI,KAAK,YAAY,EAAE,CAAC;YACzC,OAAO,CAAC,iBAAiB,GAAG,YAAY,CAAC;QAC3C,CAAC;IACH,CAAC;CACF,CAAA;AAjQY,gEAA0B;qCAA1B,0BAA0B;IADtC,IAAA,mBAAU,GAAE;IAKR,WAAA,IAAA,eAAM,EAAC,2BAA2B,CAAC,CAAA;;GAJ3B,0BAA0B,CAiQtC"}