{"version": 3, "file": "memory-conversation-state.service.js", "sourceRoot": "", "sources": ["../../../src/chat/services/memory-conversation-state.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAAmF;AACnF,2CAA+C;AAC/C,yCAAqC;AAc9B,IAAM,8BAA8B,sCAApC,MAAM,8BAA8B;IASzC,YAA6B,aAA4B;QAA5B,kBAAa,GAAb,aAAa,CAAe;QANxC,WAAM,GAAG,IAAI,eAAM,CAAC,gCAA8B,CAAC,IAAI,CAAC,CAAC;QAEzD,sBAAiB,GAA6B,IAAI,GAAG,EAAE,CAAC;QACjE,oBAAe,GAA0B,IAAI,CAAC;QAKpD,IAAI,CAAC,MAAM,GAAG;YACZ,iBAAiB,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,0BAA0B,EAAE,IAAI,CAAC;YACnF,WAAW,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,mBAAmB,EAAE,IAAI,CAAC;YACtE,iBAAiB,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,0BAA0B,EAAE,MAAM,CAAC;YACrF,aAAa,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAU,qBAAqB,EAAE,IAAI,CAAC;SAC5E,CAAC;QAGF,IAAI,CAAC,KAAK,GAAG,IAAI,oBAAQ,CAA8B;YACrD,GAAG,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW;YAC5B,GAAG,EAAE,IAAI,CAAC,MAAM,CAAC,iBAAiB,GAAG,IAAI;YACzC,cAAc,EAAE,IAAI;YACpB,cAAc,EAAE,IAAI;YACpB,OAAO,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,EAAE;gBAC9B,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;gBACpD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,SAAS,sBAAsB,CAAC,CAAC;YAChE,CAAC;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,0DAA0D,IAAI,CAAC,MAAM,CAAC,WAAW,kBAAkB,IAAI,CAAC,MAAM,CAAC,iBAAiB,GAAG,CACpI,CAAC;IACJ,CAAC;IAED,YAAY;QAEV,IAAI,IAAI,CAAC,MAAM,CAAC,iBAAiB,GAAG,CAAC,EAAE,CAAC;YACtC,IAAI,CAAC,eAAe,GAAG,WAAW,CAAC,GAAG,EAAE;gBACtC,IAAI,CAAC,2BAA2B,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;oBACjD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;gBACzD,CAAC,CAAC,CAAC;YACL,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;YAElC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,6BAA6B,IAAI,CAAC,MAAM,CAAC,iBAAiB,IAAI,CAAC,CAAC;QAClF,CAAC;IACH,CAAC;IAED,eAAe;QACb,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACzB,aAAa,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACpC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;QAC9B,CAAC;QACD,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;QACnB,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;QAC/B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,6CAA6C,CAAC,CAAC;IACjE,CAAC;IAED,KAAK,CAAC,sBAAsB,CAC1B,SAAiB,EACjB,OAA4B,EAC5B,UAAmB;QAEnB,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,GAAG,GAAG,UAAU,IAAI,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC;QACxD,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,GAAG,GAAG,IAAI,CAAC,CAAC;QAEvD,MAAM,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAElD,MAAM,OAAO,GAAwB;YACnC,SAAS;YACT,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,OAAO,EAAE;gBACP,GAAG,OAAO;gBACV,QAAQ,EAAE;oBACR,GAAG,OAAO,CAAC,QAAQ;oBACnB,YAAY,EAAE,GAAG;iBAClB;aACF;YACD,SAAS,EAAE,eAAe,EAAE,SAAS,IAAI,GAAG;YAC5C,cAAc,EAAE,GAAG;YACnB,SAAS;SACV,CAAC;QAGF,IAAI,UAAU,EAAE,CAAC;YACf,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,SAAS,EAAE,OAAO,EAAE,EAAE,GAAG,EAAE,UAAU,GAAG,IAAI,EAAE,CAAC,CAAC;QACjE,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QACrC,CAAC;QAGD,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;QAE/C,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,2CAA2C,SAAS,UAAU,OAAO,CAAC,MAAM,gBAAgB,SAAS,CAAC,WAAW,EAAE,EAAE,CACtH,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,sBAAsB,CAAC,SAAiB;QAC5C,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAE1C,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6CAA6C,SAAS,EAAE,CAAC,CAAC;YAC5E,OAAO,IAAI,CAAC;QACd,CAAC;QAGD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qCAAqC,SAAS,EAAE,CAAC,CAAC;QACpE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+BAA+B,OAAO,CAAC,OAAO,CAAC,QAAQ,EAAE,MAAM,IAAI,CAAC,WAAW,CAAC,CAAC;QACnG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oCAAoC,IAAI,CAAC,SAAS,CAClE,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,OAAO,EAAE,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,CAAC,CACrH,EAAE,CAAC,CAAC;QAGL,OAAO,CAAC,cAAc,GAAG,IAAI,IAAI,EAAE,CAAC;QACpC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QAEnC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8CAA8C,SAAS,EAAE,CAAC,CAAC;QAC7E,OAAO,OAAO,CAAC,OAAO,CAAC;IACzB,CAAC;IAED,KAAK,CAAC,yBAAyB,CAAC,SAAiB;QAC/C,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAE1C,IAAI,OAAO,EAAE,CAAC;YACZ,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;QACtD,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QAC7B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4CAA4C,SAAS,EAAE,CAAC,CAAC;IAC7E,CAAC;IAED,KAAK,CAAC,sBAAsB,CAAC,SAAiB;QAC5C,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;IACnC,CAAC;IAED,KAAK,CAAC,qBAAqB,CAAC,MAAc;QACxC,MAAM,YAAY,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAExD,IAAI,CAAC,YAAY,EAAE,CAAC;YAClB,OAAO,EAAE,CAAC;QACZ,CAAC;QAGD,MAAM,cAAc,GAAa,EAAE,CAAC;QACpC,KAAK,MAAM,SAAS,IAAI,YAAY,EAAE,CAAC;YACrC,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC9B,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACjC,CAAC;QACH,CAAC;QAGD,IAAI,cAAc,CAAC,MAAM,KAAK,YAAY,CAAC,IAAI,EAAE,CAAC;YAChD,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,GAAG,CAAC,cAAc,CAAC,CAAC,CAAC;QAC9D,CAAC;QAED,OAAO,cAAc,CAAC;IACxB,CAAC;IAED,KAAK,CAAC,2BAA2B;QAC/B,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;QAIpC,KAAK,MAAM,CAAC,MAAM,EAAE,UAAU,CAAC,IAAI,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,EAAE,CAAC;YACpE,MAAM,gBAAgB,GAAG,IAAI,GAAG,EAAU,CAAC;YAE3C,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE,CAAC;gBACnC,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;oBAC9B,gBAAgB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;gBAClC,CAAC;YACH,CAAC;YAED,IAAI,gBAAgB,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;gBAChC,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YACxC,CAAC;iBAAM,IAAI,gBAAgB,CAAC,IAAI,KAAK,UAAU,CAAC,IAAI,EAAE,CAAC;gBACrD,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,MAAM,EAAE,gBAAgB,CAAC,CAAC;YACvD,CAAC;QACH,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;QAClC,MAAM,SAAS,GAAG,WAAW,GAAG,SAAS,CAAC;QAE1C,IAAI,SAAS,GAAG,CAAC,EAAE,CAAC;YAClB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,cAAc,SAAS,gCAAgC,CAAC,CAAC;QAC3E,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,KAAK,CAAC,QAAQ;QAKZ,MAAM,KAAK,GAAG;YACZ,aAAa,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI;YAC9B,cAAc,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI;YAC/B,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC,CAAC,CAAC,SAAS;SAChF,CAAC;QAEF,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,cAAc,CAAC,MAAc,EAAE,SAAiB;QACtD,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;YACxC,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC;QAChD,CAAC;QACD,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,MAAM,CAAE,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;IACrD,CAAC;IAEO,mBAAmB,CAAC,MAAc,EAAE,SAAiB;QAC3D,MAAM,YAAY,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACxD,IAAI,YAAY,EAAE,CAAC;YACjB,YAAY,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAC/B,IAAI,YAAY,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;gBAC5B,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YACxC,CAAC;QACH,CAAC;IACH,CAAC;IAEO,mBAAmB;QAEzB,IAAI,SAAS,GAAG,CAAC,CAAC;QAElB,KAAK,MAAM,CAAC,SAAS,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,CAAC;YAExD,SAAS,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;YAClC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;QAClD,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;CACF,CAAA;AA1OY,wEAA8B;yCAA9B,8BAA8B;IAD1C,IAAA,mBAAU,GAAE;qCAUiC,sBAAa;GAT9C,8BAA8B,CA0O1C"}