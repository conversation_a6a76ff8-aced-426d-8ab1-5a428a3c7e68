"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var MemoryConversationStateService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.MemoryConversationStateService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const lru_cache_1 = require("lru-cache");
let MemoryConversationStateService = MemoryConversationStateService_1 = class MemoryConversationStateService {
    constructor(configService) {
        this.configService = configService;
        this.logger = new common_1.Logger(MemoryConversationStateService_1.name);
        this.userSessionsIndex = new Map();
        this.cleanupInterval = null;
        this.config = {
            defaultTtlSeconds: this.configService.get('CHAT_SESSION_TTL_SECONDS', 3600),
            maxSessions: this.configService.get('CHAT_MAX_SESSIONS', 1000),
            cleanupIntervalMs: this.configService.get('CHAT_CLEANUP_INTERVAL_MS', 300000),
            enableMetrics: this.configService.get('CHAT_ENABLE_METRICS', true),
        };
        this.cache = new lru_cache_1.LRUCache({
            max: this.config.maxSessions,
            ttl: this.config.defaultTtlSeconds * 1000,
            updateAgeOnGet: true,
            updateAgeOnHas: true,
            dispose: (session, sessionId) => {
                this.removeFromUserIndex(session.userId, sessionId);
                this.logger.debug(`Session ${sessionId} disposed from cache`);
            },
        });
        this.logger.log(`Initialized memory conversation state service with max ${this.config.maxSessions} sessions, TTL ${this.config.defaultTtlSeconds}s`);
    }
    onModuleInit() {
        if (this.config.cleanupIntervalMs > 0) {
            this.cleanupInterval = setInterval(() => {
                this.cleanupExpiredConversations().catch((error) => {
                    this.logger.error('Error during cleanup', error.stack);
                });
            }, this.config.cleanupIntervalMs);
            this.logger.log(`Started cleanup interval: ${this.config.cleanupIntervalMs}ms`);
        }
    }
    onModuleDestroy() {
        if (this.cleanupInterval) {
            clearInterval(this.cleanupInterval);
            this.cleanupInterval = null;
        }
        this.cache.clear();
        this.userSessionsIndex.clear();
        this.logger.log('Memory conversation state service destroyed');
    }
    async setConversationContext(sessionId, context, ttlSeconds) {
        const now = new Date();
        const ttl = ttlSeconds || this.config.defaultTtlSeconds;
        const expiresAt = new Date(now.getTime() + ttl * 1000);
        const existingSession = this.cache.get(sessionId);
        const session = {
            sessionId,
            userId: context.userId,
            context: {
                ...context,
                metadata: {
                    ...context.metadata,
                    lastActiveAt: now,
                },
            },
            createdAt: existingSession?.createdAt || now,
            lastAccessedAt: now,
            expiresAt,
        };
        if (ttlSeconds) {
            this.cache.set(sessionId, session, { ttl: ttlSeconds * 1000 });
        }
        else {
            this.cache.set(sessionId, session);
        }
        this.addToUserIndex(context.userId, sessionId);
        this.logger.debug(`Stored conversation context for session ${sessionId}, user ${context.userId}, expires at ${expiresAt.toISOString()}`);
    }
    async getConversationContext(sessionId) {
        const session = this.cache.get(sessionId);
        if (!session) {
            this.logger.debug(`No conversation context found for session ${sessionId}`);
            return null;
        }
        this.logger.debug(`🔍 CACHE DEBUG: Retrieved session ${sessionId}`);
        this.logger.debug(`🔍 CACHE DEBUG: Session has ${session.context.messages?.length || 0} messages`);
        this.logger.debug(`🔍 CACHE DEBUG: Last 3 messages: ${JSON.stringify((session.context.messages || []).slice(-3).map(m => ({ role: m.role, content: m.content.substring(0, 30) + '...' })))}`);
        session.lastAccessedAt = new Date();
        this.cache.set(sessionId, session);
        this.logger.debug(`Retrieved conversation context for session ${sessionId}`);
        return session.context;
    }
    async deleteConversationContext(sessionId) {
        const session = this.cache.get(sessionId);
        if (session) {
            this.removeFromUserIndex(session.userId, sessionId);
        }
        this.cache.delete(sessionId);
        this.logger.debug(`Deleted conversation context for session ${sessionId}`);
    }
    async hasConversationContext(sessionId) {
        return this.cache.has(sessionId);
    }
    async getUserActiveSessions(userId) {
        const userSessions = this.userSessionsIndex.get(userId);
        if (!userSessions) {
            return [];
        }
        const activeSessions = [];
        for (const sessionId of userSessions) {
            if (this.cache.has(sessionId)) {
                activeSessions.push(sessionId);
            }
        }
        if (activeSessions.length !== userSessions.size) {
            this.userSessionsIndex.set(userId, new Set(activeSessions));
        }
        return activeSessions;
    }
    async cleanupExpiredConversations() {
        const initialSize = this.cache.size;
        for (const [userId, sessionIds] of this.userSessionsIndex.entries()) {
            const activeSessionIds = new Set();
            for (const sessionId of sessionIds) {
                if (this.cache.has(sessionId)) {
                    activeSessionIds.add(sessionId);
                }
            }
            if (activeSessionIds.size === 0) {
                this.userSessionsIndex.delete(userId);
            }
            else if (activeSessionIds.size !== sessionIds.size) {
                this.userSessionsIndex.set(userId, activeSessionIds);
            }
        }
        const finalSize = this.cache.size;
        const cleanedUp = initialSize - finalSize;
        if (cleanedUp > 0) {
            this.logger.log(`Cleaned up ${cleanedUp} expired conversation sessions`);
        }
        return cleanedUp;
    }
    async getStats() {
        const stats = {
            totalSessions: this.cache.size,
            activeSessions: this.cache.size,
            memoryUsage: this.config.enableMetrics ? this.estimateMemoryUsage() : undefined,
        };
        return stats;
    }
    addToUserIndex(userId, sessionId) {
        if (!this.userSessionsIndex.has(userId)) {
            this.userSessionsIndex.set(userId, new Set());
        }
        this.userSessionsIndex.get(userId).add(sessionId);
    }
    removeFromUserIndex(userId, sessionId) {
        const userSessions = this.userSessionsIndex.get(userId);
        if (userSessions) {
            userSessions.delete(sessionId);
            if (userSessions.size === 0) {
                this.userSessionsIndex.delete(userId);
            }
        }
    }
    estimateMemoryUsage() {
        let totalSize = 0;
        for (const [sessionId, session] of this.cache.entries()) {
            totalSize += sessionId.length * 2;
            totalSize += JSON.stringify(session).length * 2;
        }
        return totalSize;
    }
};
exports.MemoryConversationStateService = MemoryConversationStateService;
exports.MemoryConversationStateService = MemoryConversationStateService = MemoryConversationStateService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], MemoryConversationStateService);
//# sourceMappingURL=memory-conversation-state.service.js.map