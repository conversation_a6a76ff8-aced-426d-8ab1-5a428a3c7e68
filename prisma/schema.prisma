generator client {
  provider        = "prisma-client-js"
  output          = "../generated/prisma"
  previewFeatures = ["fullTextSearchPostgres", "multiSchema"]
  binaryTargets   = ["native", "debian-openssl-3.0.x"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
  schemas  = ["public"]
}

model User {
  id                       String                    @id @default(uuid()) @db.Uuid
  authUserId               String                    @unique @map("auth_user_id") @db.Uuid
  username                 String?                   @unique
  displayName              String?                   @map("display_name")
  email                    String                    @unique
  role                     UserRole                  @default(USER)
  status                   UserStatus                @default(ACTIVE)
  technicalLevel           TechnicalLevel?           @map("technical_level")
  profilePictureUrl        String?                   @map("profile_picture_url")
  bio                      String?
  socialLinks              Json?                     @map("social_links")
  createdAt                DateTime                  @default(now()) @map("created_at")
  updatedAt                DateTime                  @updatedAt @map("updated_at")
  lastLogin                DateTime?                 @map("last_login")
  bookmarksCount           Int                       @default(0) @map("bookmarks_count")
  reputationScore          Int                       @default(0) @map("reputation_score")
  requestsFulfilled        Int                       @default(0) @map("requests_fulfilled")
  requestsMade             Int                       @default(0) @map("requests_made")
  reviewsCount             Int                       @default(0) @map("reviews_count")
  toolsApproved            Int                       @default(0) @map("tools_approved")
  toolsSubmitted           Int                       @default(0) @map("tools_submitted")
  submittedEntities        Entity[]                  @relation("Submitter")
  entityBadgesGranted      EntityBadge[]             @relation("EntityBadgesGranted")
  profileActivities        ProfileActivity[]
  reviewVotes              ReviewVote[]              @relation("UserReviewVotes")
  reviewsModerated         Review[]                  @relation("ModeratorReviews")
  reviews                  Review[]
  toolRequests             ToolRequest[]
  userActivityLogTargets   UserActivityLog[]         @relation("TargetUserLogs")
  userActivityLogs         UserActivityLog[]         @relation("UserLogs")
  badgesGranted            UserBadge[]               @relation("UserBadgesGranted")
  userBadges               UserBadge[]               @relation("UserBadges")
  userFollowedCategories   UserFollowedCategory[]    @relation("UserFollowedCategories")
  userFollowedTags         UserFollowedTag[]         @relation("UserFollowedTags")
  userNotificationSettings UserNotificationSettings?
  userPreferences          UserPreferences?
  userSavedEntities        UserSavedEntity[]         @relation("UserSavedEntities")
  userUpvotes              UserUpvote[]
  reviewedSubmissions      UserSubmittedTool[]       @relation("ReviewerSubmissions")
  userSubmittedTools       UserSubmittedTool[]

  @@map("users")
  @@schema("public")
}

model EntityType {
  id          String   @id @default(uuid()) @db.Uuid
  name        String   @unique
  description String?
  slug        String   @unique
  iconUrl     String?  @map("icon_url")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")
  entities    Entity[]

  @@map("entity_types")
  @@schema("public")
}

model Entity {
  id                            String                         @id @default(uuid()) @db.Uuid
  entityTypeId                  String                         @map("entity_type_id") @db.Uuid
  name                          String                         @unique
  shortDescription              String?                        @map("short_description")
  description                   String?
  logoUrl                       String?                        @map("logo_url")
  websiteUrl                    String?                        @map("website_url")
  documentationUrl              String?                        @map("documentation_url")
  contactUrl                    String?                        @map("contact_url")
  privacyPolicyUrl              String?                        @map("privacy_policy_url")
  foundedYear                   Int?                           @map("founded_year")
  socialLinks                   Json?                          @map("social_links")
  status                        EntityStatus                   @default(PENDING)
  avgRating                     Float                          @default(0) @map("avg_rating")
  reviewCount                   Int                            @default(0) @map("review_count")
  upvoteCount                   Int                            @default(0) @map("upvote_count")
  createdAt                     DateTime                       @default(now()) @map("created_at")
  updatedAt                     DateTime                       @updatedAt @map("updated_at")
  legacyId                      String?                        @map("legacy_id")
  submitterId                   String                         @map("submitter_id") @db.Uuid
  affiliateStatus               AffiliateStatus?               @default(NONE)
  locationSummary               String?                        @map("location_summary")
  metaDescription               String?                        @map("meta_description")
  metaTitle                     String?                        @map("meta_title")
  refLink                       String?                        @map("ref_link")
  scrapedReviewCount            Int?                           @map("scraped_review_count")
  scrapedReviewSentimentLabel   String?                        @map("scraped_review_sentiment_label")
  scrapedReviewSentimentScore   Float?                         @map("scraped_review_sentiment_score")
  vectorEmbedding               Unsupported("vector")?         @map("vector_embedding")
  ftsDocument                   Unsupported("tsvector")?       @map("ftsDocument")
  employeeCountRange            EmployeeCountRange?            @map("employee_count_range")
  fundingStage                  FundingStage?                  @map("funding_stage")
  slug                          String                         @unique
  entityType                    EntityType                     @relation(fields: [entityTypeId], references: [id])
  submitter                     User                           @relation("Submitter", fields: [submitterId], references: [id])
  entityBadges                  EntityBadge[]
  entityCategories              EntityCategory[]
  entityDetailsAgency           EntityDetailsAgency?
  entityDetailsBook             EntityDetailsBook?
  entityDetailsBounty           EntityDetailsBounty?
  entityDetailsCommunity        EntityDetailsCommunity?
  entityDetailsContentCreator   EntityDetailsContentCreator?
  entityDetailsCourse           EntityDetailsCourse?
  entityDetailsDataset          EntityDetailsDataset?
  entityDetailsEvent            EntityDetailsEvent?
  entityDetailsGrant            EntityDetailsGrant?
  entityDetailsHardware         EntityDetailsHardware?
  entityDetailsInvestor         EntityDetailsInvestor?
  entityDetailsJob              EntityDetailsJob?
  entityDetailsModel            EntityDetailsModel?
  entityDetailsNews             EntityDetailsNews?
  entityDetailsNewsletter       EntityDetailsNewsletter?
  entityDetailsPlatform         EntityDetailsPlatform?
  entityDetailsPodcast          EntityDetailsPodcast?
  entityDetailsProjectReference EntityDetailsProjectReference?
  entityDetailsResearchPaper    EntityDetailsResearchPaper?
  entityDetailsServiceProvider  EntityDetailsServiceProvider?
  entityDetailsSoftware         EntityDetailsSoftware?
  entityDetailsTool             EntityDetailsTool?
  entityFeatures                EntityFeature[]
  entityTags                    EntityTag[]
  profileActivities             ProfileActivity[]
  reviews                       Review[]
  userActivityLogs              UserActivityLog[]              @relation("EntityLogs")
  userSavedEntities             UserSavedEntity[]
  userUpvotes                   UserUpvote[]
  userSubmittedTools            UserSubmittedTool[]

  @@map("entities")
  @@schema("public")
}

model Category {
  id                     String                 @id @default(uuid()) @db.Uuid
  name                   String                 @unique
  description            String?
  slug                   String                 @unique
  iconUrl                String?                @map("icon_url")
  createdAt              DateTime               @default(now()) @map("created_at")
  updatedAt              DateTime               @updatedAt @map("updated_at")
  parentId               String?                @map("parent_id") @db.Uuid
  parent                 Category?              @relation("CategoryToParent", fields: [parentId], references: [id])
  children               Category[]             @relation("CategoryToParent")
  entityCategories       EntityCategory[]
  userActivityLogs       UserActivityLog[]
  userFollowedCategories UserFollowedCategory[]

  @@map("categories")
  @@schema("public")
}

model Tag {
  id               String            @id @default(uuid()) @db.Uuid
  name             String            @unique
  description      String?
  slug             String            @unique
  createdAt        DateTime          @default(now()) @map("created_at")
  updatedAt        DateTime          @updatedAt @map("updated_at")
  entityTags       EntityTag[]
  userActivityLogs UserActivityLog[] @relation("TagLogs")
  userFollowed     UserFollowedTag[]

  @@map("tags")
  @@schema("public")
}

model EntityTag {
  entityId   String   @map("entity_id") @db.Uuid
  tagId      String   @map("tag_id") @db.Uuid
  assignedBy String   @map("assigned_by") @db.Uuid
  createdAt  DateTime @default(now()) @map("created_at")
  id         String   @id @default(uuid()) @db.Uuid
  entity     Entity   @relation(fields: [entityId], references: [id], onDelete: Cascade)
  tag        Tag      @relation(fields: [tagId], references: [id], onDelete: Cascade)

  @@unique([entityId, tagId], map: "uq_entity_tag")
  @@map("entity_tags")
  @@schema("public")
}

model EntityCategory {
  entityId   String   @map("entity_id") @db.Uuid
  categoryId String   @map("category_id") @db.Uuid
  assignedAt DateTime @default(now()) @map("assigned_at")
  assignedBy String   @map("assigned_by") @db.Uuid
  category   Category @relation(fields: [categoryId], references: [id], onDelete: Cascade)
  entity     Entity   @relation(fields: [entityId], references: [id], onDelete: Cascade)

  @@id([entityId, categoryId])
  @@map("entity_categories")
  @@schema("public")
}

model Review {
  id               String            @id @default(uuid()) @db.Uuid
  entityId         String            @map("entity_id") @db.Uuid
  userId           String            @map("user_id") @db.Uuid
  rating           Int
  title            String?
  status           ReviewStatus      @default(PENDING)
  moderationNotes  String?           @map("moderation_notes")
  createdAt        DateTime          @default(now()) @map("created_at")
  updatedAt        DateTime          @updatedAt @map("updated_at")
  content          String?
  downvotes        Int               @default(0)
  moderatorId      String?           @map("moderator_id") @db.Uuid
  upvotes          Int               @default(0)
  reviewVotes      ReviewVote[]
  entity           Entity            @relation(fields: [entityId], references: [id], onDelete: Cascade)
  moderator        User?             @relation("ModeratorReviews", fields: [moderatorId], references: [id])
  user             User              @relation(fields: [userId], references: [id], onDelete: Cascade)
  userActivityLogs UserActivityLog[] @relation("ReviewLogs")

  @@unique([entityId, userId], map: "uq_review_entity_user")
  @@map("reviews")
  @@schema("public")
}

model ReviewVote {
  reviewId  String   @map("review_id") @db.Uuid
  userId    String   @map("user_id") @db.Uuid
  createdAt DateTime @default(now()) @map("created_at")
  id        String   @id @default(uuid()) @db.Uuid
  isUpvote  Boolean  @map("is_upvote")
  review    Review   @relation(fields: [reviewId], references: [id], onDelete: Cascade)
  user      User     @relation("UserReviewVotes", fields: [userId], references: [id], onDelete: Cascade)

  @@unique([reviewId, userId], map: "uq_review_vote_user")
  @@map("review_votes")
  @@schema("public")
}

model EntityDetailsTool {
  entityId             String          @unique @map("entity_id") @db.Uuid
  learningCurve        LearningCurve?  @map("learning_curve")
  targetAudience       Json?           @map("target_audience")
  keyFeatures          Json?           @map("key_features")
  useCases             Json?           @map("use_cases")
  pricingModel         PricingModel?   @map("pricing_model")
  priceRange           PriceRange?     @map("price_range")
  pricingDetails       String?         @map("pricing_details")
  pricingUrl           String?         @map("pricing_url")
  hasFreeTier          Boolean?        @map("has_free_tier")
  integrations         Json?
  apiAccess            Boolean?        @map("api_access")
  communityUrl         String?         @map("community_url")
  customizationLevel   String?         @map("customization_level")
  demoAvailable        Boolean?        @map("demo_available")
  deploymentOptions    Json?           @map("deployment_options")
  frameworks           Json?
  hasLiveChat          Boolean?        @map("has_live_chat")
  libraries            Json?
  mobileSupport        Boolean?        @map("mobile_support")
  openSource           Boolean?        @map("open_source")
  programmingLanguages Json?           @map("programming_languages")
  supportChannels      Json?           @map("support_channels")
  supportEmail         String?         @map("support_email")
  supportedOs          Json?           @map("supported_os")
  trialAvailable       Boolean?        @map("trial_available")
  hasApi               Boolean?        @map("has_api")
  id                   String          @id @default(uuid()) @db.Uuid
  platforms            Json?
  technicalLevel       TechnicalLevel? @map("technical_level")
  entity               Entity          @relation(fields: [entityId], references: [id], onDelete: Cascade)

  @@map("entity_details_tool")
  @@schema("public")
}

model EntityDetailsAgency {
  entityId         String  @unique @map("entity_id") @db.Uuid
  servicesOffered  Json?   @map("services_offered")
  industryFocus    Json?   @map("industry_focus")
  targetClientSize Json?   @map("target_client_size")
  targetAudience   Json?   @map("target_audience")
  locationSummary  String? @map("location_summary")
  portfolioUrl     String? @map("portfolio_url")
  pricingInfo      String? @map("pricing_info")
  id               String  @id @default(uuid()) @db.Uuid
  entity           Entity  @relation(fields: [entityId], references: [id], onDelete: Cascade)

  @@map("entity_details_agency")
  @@schema("public")
}

model EntityDetailsContentCreator {
  entityId          String  @unique @map("entity_id") @db.Uuid
  creatorName       String? @map("creator_name")
  primaryPlatform   String? @map("primary_platform")
  focusAreas        Json?   @map("focus_areas")
  followerCount     Int?    @map("follower_count")
  exampleContentUrl String? @map("example_content_url")
  id                String  @id @default(uuid()) @db.Uuid
  entity            Entity  @relation(fields: [entityId], references: [id], onDelete: Cascade)

  @@map("entity_details_content_creator")
  @@schema("public")
}

model EntityDetailsCommunity {
  entityId       String  @unique @map("entity_id") @db.Uuid
  platform       String?
  memberCount    Int?    @map("member_count")
  focusTopics    Json?   @map("focus_topics")
  rulesUrl       String? @map("rules_url")
  inviteUrl      String? @map("invite_url")
  mainChannelUrl String? @map("main_channel_url")
  id             String  @id @default(uuid()) @db.Uuid
  entity         Entity  @relation(fields: [entityId], references: [id], onDelete: Cascade)

  @@map("entity_details_community")
  @@schema("public")
}

model EntityDetailsNewsletter {
  entityId        String  @unique @map("entity_id") @db.Uuid
  frequency       String?
  mainTopics      Json?   @map("main_topics")
  archiveUrl      String? @map("archive_url")
  subscribeUrl    String? @map("subscribe_url")
  authorName      String? @map("author_name")
  subscriberCount Int?    @default(0) @map("subscriber_count")
  id              String  @id @default(uuid()) @db.Uuid
  entity          Entity  @relation(fields: [entityId], references: [id], onDelete: Cascade)

  @@map("entity_details_newsletter")
  @@schema("public")
}

model EntityDetailsCourse {
  entityId             String      @unique @map("entity_id") @db.Uuid
  instructorName       String?     @map("instructor_name")
  durationText         String?     @map("duration_text")
  skillLevel           SkillLevel? @map("skill_level")
  prerequisites        String?
  syllabusUrl          String?     @map("syllabus_url")
  enrollmentCount      Int?        @map("enrollment_count")
  certificateAvailable Boolean?    @default(false) @map("certificate_available")
  id                   String      @id @default(uuid()) @db.Uuid
  entity               Entity      @relation(fields: [entityId], references: [id], onDelete: Cascade)

  @@map("entity_details_course")
  @@schema("public")
}

model UserSavedEntity {
  userId    String   @map("user_id") @db.Uuid
  entityId  String   @map("entity_id") @db.Uuid
  createdAt DateTime @default(now()) @map("created_at")
  id        String   @id @default(uuid()) @db.Uuid
  entity    Entity   @relation(fields: [entityId], references: [id], onDelete: Cascade)
  user      User     @relation("UserSavedEntities", fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, entityId], map: "uq_user_saved_entity")
  @@map("user_saved_entities")
  @@schema("public")
}

model UserUpvote {
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId    String   @map("user_id") @db.Uuid
  entity    Entity   @relation(fields: [entityId], references: [id], onDelete: Cascade)
  entityId  String   @map("entity_id") @db.Uuid
  createdAt DateTime @default(now()) @map("created_at")

  @@id([userId, entityId]) // Compound primary key ensures a user can only upvote an entity once
  @@map("user_upvotes")
  @@schema("public")
}

model UserFollowedTag {
  userId    String   @map("user_id") @db.Uuid
  tagId     String   @map("tag_id") @db.Uuid
  createdAt DateTime @default(now()) @map("created_at")
  id        String   @id @default(uuid()) @db.Uuid
  tag       Tag      @relation(fields: [tagId], references: [id], onDelete: Cascade)
  user      User     @relation("UserFollowedTags", fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, tagId], map: "uq_user_followed_tag")
  @@map("user_followed_tags")
  @@schema("public")
}

model UserFollowedCategory {
  userId     String   @map("user_id") @db.Uuid
  categoryId String   @map("category_id") @db.Uuid
  followedAt DateTime @default(now()) @map("followed_at")
  category   Category @relation(fields: [categoryId], references: [id], onDelete: Cascade)
  user       User     @relation("UserFollowedCategories", fields: [userId], references: [id], onDelete: Cascade)

  @@id([userId, categoryId])
  @@map("user_followed_categories")
  @@schema("public")
}

model UserActivityLog {
  id           String     @id @default(uuid()) @db.Uuid
  userId       String     @map("user_id") @db.Uuid
  actionType   ActionType @map("action_type")
  entityId     String?    @map("entity_id") @db.Uuid
  categoryId   String?    @map("category_id") @db.Uuid
  tagId        String?    @map("tag_id") @db.Uuid
  reviewId     String?    @map("review_id") @db.Uuid
  targetUserId String?    @map("target_user_id") @db.Uuid
  details      Json?
  createdAt    DateTime   @default(now()) @map("created_at")
  category     Category?  @relation(fields: [categoryId], references: [id])
  entity       Entity?    @relation("EntityLogs", fields: [entityId], references: [id])
  review       Review?    @relation("ReviewLogs", fields: [reviewId], references: [id])
  tag          Tag?       @relation("TagLogs", fields: [tagId], references: [id])
  targetUser   User?      @relation("TargetUserLogs", fields: [targetUserId], references: [id])
  user         User       @relation("UserLogs", fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_activity_logs")
  @@schema("public")
}

model UserNotificationSettings {
  userId                     String   @unique @map("user_id") @db.Uuid
  emailNewsletter            Boolean  @default(true) @map("email_newsletter")
  createdAt                  DateTime @default(now()) @map("created_at")
  updatedAt                  DateTime @updatedAt @map("updated_at")
  emailMarketing             Boolean  @default(true) @map("email_marketing")
  emailOnNewEntityInFollowed Boolean  @default(true) @map("email_on_new_entity_in_followed")
  emailOnNewFollower         Boolean  @default(true) @map("email_on_new_follower")
  emailOnNewReview           Boolean  @default(true) @map("email_on_new_review")
  emailOnReviewResponse      Boolean  @default(true) @map("email_on_review_response")
  id                         String   @id @default(uuid()) @db.Uuid
  user                       User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_notification_settings")
  @@schema("public")
}

model BadgeType {
  id           String        @id @default(uuid()) @db.Uuid
  name         String        @unique
  description  String?
  iconUrl      String?       @map("icon_url")
  scope        BadgeScope
  createdAt    DateTime      @default(now()) @map("created_at")
  updatedAt    DateTime      @updatedAt @map("updated_at")
  criteria     Json?
  entityBadges EntityBadge[] @relation("BadgeTypeEntity")
  userBadges   UserBadge[]   @relation("BadgeTypeUser")

  @@map("badge_types")
  @@schema("public")
}

model UserBadge {
  id            String    @id @default(uuid()) @db.Uuid
  userId        String    @map("user_id") @db.Uuid
  badgeTypeId   String    @map("badge_type_id") @db.Uuid
  grantedAt     DateTime  @default(now()) @map("granted_at")
  notes         String?
  grantedBy     String?   @map("granted_by") @db.Uuid
  badgeType     BadgeType @relation("BadgeTypeUser", fields: [badgeTypeId], references: [id], onDelete: Cascade)
  grantedByUser User?     @relation("UserBadgesGranted", fields: [grantedBy], references: [id])
  user          User      @relation("UserBadges", fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, badgeTypeId], map: "uq_user_badge")
  @@map("user_badges")
  @@schema("public")
}

model EntityBadge {
  id            String    @id @default(uuid()) @db.Uuid
  entityId      String    @map("entity_id") @db.Uuid
  badgeTypeId   String    @map("badge_type_id") @db.Uuid
  grantedAt     DateTime  @default(now()) @map("granted_at")
  notes         String?
  expiresAt     DateTime? @map("expires_at")
  grantedBy     String?   @map("granted_by") @db.Uuid
  badgeType     BadgeType @relation("BadgeTypeEntity", fields: [badgeTypeId], references: [id], onDelete: Cascade)
  entity        Entity    @relation(fields: [entityId], references: [id], onDelete: Cascade)
  grantedByUser User?     @relation("EntityBadgesGranted", fields: [grantedBy], references: [id])

  @@unique([entityId, badgeTypeId], map: "uq_entity_badge")
  @@map("entity_badges")
  @@schema("public")
}

model EntityDetailsDataset {
  entityId         String   @unique @map("entity_id") @db.Uuid
  createdAt        DateTime @default(now()) @map("created_at")
  updatedAt        DateTime @updatedAt @map("updated_at")
  accessNotes      String?  @map("access_notes")
  description      String?
  license          String?
  sizeInBytes      BigInt?  @map("size_in_bytes")
  sourceUrl        String?  @map("source_url")
  collectionMethod String?  @map("collection_method")
  id               String   @id @default(uuid()) @db.Uuid
  updateFrequency  String?  @map("update_frequency")
  format           String?
  entity           Entity   @relation(fields: [entityId], references: [id], onDelete: Cascade)

  @@map("entity_details_dataset")
  @@schema("public")
}

model EntityDetailsResearchPaper {
  entityId            String    @unique @map("entity_id") @db.Uuid
  createdAt           DateTime  @default(now()) @map("created_at")
  updatedAt           DateTime  @updatedAt @map("updated_at")
  abstract            String?
  citationCount       Int?      @map("citation_count")
  doi                 String?
  journalOrConference String?   @map("journal_or_conference")
  publicationDate     DateTime? @map("publication_date")
  id                  String    @id @default(uuid()) @db.Uuid
  pdfUrl              String?   @map("pdf_url")
  authors             String[]
  researchAreas       String[]  @map("research_areas")
  publicationVenues   String[]  @map("publication_venues")
  keywords            String[]
  arxivId             String?   @map("arxiv_id")
  entity              Entity    @relation(fields: [entityId], references: [id], onDelete: Cascade)

  @@map("entity_details_research_paper")
  @@schema("public")
}

model EntityDetailsSoftware {
  entityId              String        @unique @map("entity_id") @db.Uuid
  createdAt             DateTime      @default(now()) @map("created_at")
  updatedAt             DateTime      @updatedAt @map("updated_at")
  currentVersion        String?       @map("current_version")
  licenseType           String?       @map("license_type")
  communityUrl          String?       @map("community_url")
  hasFreeTier           Boolean?      @map("has_free_tier")
  hasLiveChat           Boolean?      @map("has_live_chat")
  priceRange            PriceRange?   @map("price_range")
  pricingDetails        String?       @map("pricing_details")
  pricingModel          PricingModel? @map("pricing_model")
  pricingUrl            String?       @map("pricing_url")
  supportEmail          String?       @map("support_email")
  apiAccess             Boolean?      @map("api_access")
  customizationLevel    String?       @map("customization_level")
  demoAvailable         Boolean?      @map("demo_available")
  deploymentOptions     String[]      @map("deployment_options")
  frameworks            String[]
  hasApi                Boolean?      @map("has_api")
  id                    String        @id @default(uuid()) @db.Uuid
  keyFeatures           String[]      @map("key_features")
  libraries             String[]
  mobileSupport         Boolean?      @map("mobile_support")
  openSource            Boolean?      @map("open_source")
  supportChannels       String[]      @map("support_channels")
  supportedOs           String[]      @map("supported_os")
  targetAudience        String[]      @map("target_audience")
  trialAvailable        Boolean?      @map("trial_available")
  integrations          String[]
  platformCompatibility String[]      @map("platform_compatibility")
  programmingLanguages  String[]      @map("programming_languages")
  useCases              String[]      @map("use_cases")
  repositoryUrl         String?       @map("repository_url")
  releaseDate           DateTime?     @map("release_date")
  entity                Entity        @relation(fields: [entityId], references: [id], onDelete: Cascade)

  @@map("entity_details_software")
  @@schema("public")
}

model EntityDetailsModel {
  entityId           String   @unique @map("entity_id") @db.Uuid
  createdAt          DateTime @default(now()) @map("created_at")
  updatedAt          DateTime @updatedAt @map("updated_at")
  license            String?
  modelArchitecture  String?  @map("model_architecture")
  trainingDataset    String?  @map("training_dataset")
  deploymentOptions  String[] @map("deployment_options")
  frameworks         String[]
  id                 String   @id @default(uuid()) @db.Uuid
  inputDataTypes     String[] @map("input_data_types")
  libraries          String[]
  outputDataTypes    String[] @map("output_data_types")
  performanceMetrics Json?    @map("performance_metrics")
  targetAudience     String[] @map("target_audience")
  useCases           String[] @map("use_cases")
  entity             Entity   @relation(fields: [entityId], references: [id], onDelete: Cascade)

  @@map("entity_details_model")
  @@schema("public")
}

model EntityDetailsProjectReference {
  entityId        String   @unique @map("entity_id") @db.Uuid
  createdAt       DateTime @default(now()) @map("created_at")
  updatedAt       DateTime @updatedAt @map("updated_at")
  forks           Int?
  id              String   @id @default(uuid()) @db.Uuid
  keyTechnologies String[] @map("key_technologies")
  license         String?
  repositoryUrl   String?  @map("repository_url")
  stars           Int?
  status          String?
  useCases        String[] @map("use_cases")
  contributors    Int?
  entity          Entity   @relation(fields: [entityId], references: [id], onDelete: Cascade)

  @@map("entity_details_project_reference")
  @@schema("public")
}

model EntityDetailsServiceProvider {
  entityId                String   @unique @map("entity_id") @db.Uuid
  createdAt               DateTime @default(now()) @map("created_at")
  updatedAt               DateTime @updatedAt @map("updated_at")
  companySizeFocus        String?  @map("company_size_focus")
  id                      String   @id @default(uuid()) @db.Uuid
  industrySpecializations String[] @map("industry_specializations")
  locationSummary         String?  @map("location_summary")
  portfolioUrl            String?  @map("portfolio_url")
  pricingInfo             String?  @map("pricing_info")
  servicesOffered         String[] @map("services_offered")
  targetAudience          String[] @map("target_audience")
  entity                  Entity   @relation(fields: [entityId], references: [id], onDelete: Cascade)

  @@map("entity_details_service_provider")
  @@schema("public")
}

model EntityDetailsInvestor {
  entityId           String   @unique @map("entity_id") @db.Uuid
  createdAt          DateTime @default(now()) @map("created_at")
  updatedAt          DateTime @updatedAt @map("updated_at")
  contactEmail       String?  @map("contact_email")
  applicationUrl     String?  @map("application_url")
  focusAreas         String[] @map("focus_areas")
  id                 String   @id @default(uuid()) @db.Uuid
  investmentStages   String[] @map("investment_stages")
  investorType       String?  @map("investor_type")
  locationSummary    String?  @map("location_summary")
  notableInvestments String[] @map("notable_investments")
  portfolioSize      Int?     @map("portfolio_size")
  entity             Entity   @relation(fields: [entityId], references: [id], onDelete: Cascade)

  @@map("entity_details_investor")
  @@schema("public")
}

model EntityDetailsEvent {
  entityId             String           @unique @map("entity_id") @db.Uuid
  createdAt            DateTime         @default(now()) @map("created_at")
  updatedAt            DateTime         @updatedAt @map("updated_at")
  endDate              DateTime?        @map("end_date")
  location             String?
  price                String?
  registrationUrl      String?          @map("registration_url")
  startDate            DateTime?        @map("start_date")
  eventType            String?          @map("event_type")
  id                   String           @id @default(uuid()) @db.Uuid
  isOnline             Boolean?         @map("is_online")
  keySpeakers          String[]         @map("key_speakers")
  targetAudience       String[]         @map("target_audience")
  topics               String[]
  registrationRequired Boolean?         @default(false) @map("registration_required")
  capacity             Int?
  organizer            String?
  eventFormat          EventFormatEnum? @map("event_format")
  entity               Entity           @relation(fields: [entityId], references: [id], onDelete: Cascade)

  @@map("entity_details_event")
  @@schema("public")
}

model EntityDetailsJob {
  entityId            String                @unique @map("entity_id") @db.Uuid
  createdAt           DateTime              @default(now()) @map("created_at")
  updatedAt           DateTime              @updatedAt @map("updated_at")
  applicationUrl      String?               @map("application_url")
  companyName         String?               @map("company_name")
  experienceLevel     ExperienceLevelEnum?  @map("experience_level")
  id                  String                @id @default(uuid()) @db.Uuid
  isRemote            Boolean?              @map("is_remote")
  jobType             String?               @map("job_type")
  keyResponsibilities String[]              @map("key_responsibilities")
  location            String?
  requiredSkills      String[]              @map("required_skills")
  salaryMax           Float?                @map("salary_max")
  salaryMin           Float?                @map("salary_min")
  jobDescription      String?               @map("job_description")
  employmentTypes     EmploymentTypeEnum[]  @map("employment_types")
  locationTypes       LocationTypeEnum[]    @map("location_types")
  benefits            String[]
  remotePolicy        String?               @map("remote_policy")
  visaSponsorship     Boolean?              @default(false) @map("visa_sponsorship")
  entity              Entity                @relation(fields: [entityId], references: [id], onDelete: Cascade)

  @@map("entity_details_job")
  @@schema("public")
}

model EntityDetailsGrant {
  entityId       String    @unique @map("entity_id") @db.Uuid
  createdAt      DateTime  @default(now()) @map("created_at")
  updatedAt      DateTime  @updatedAt @map("updated_at")
  applicationUrl String?   @map("application_url")
  amount         String?
  deadline       DateTime?
  eligibility    String?
  focusAreas     String[]  @map("focus_areas")
  funderName     String?   @map("funder_name")
  grantType      String?   @map("grant_type")
  id             String    @id @default(uuid()) @db.Uuid
  location       String?
  entity         Entity    @relation(fields: [entityId], references: [id], onDelete: Cascade)

  @@map("entity_details_grant")
  @@schema("public")
}

model EntityDetailsBounty {
  entityId        String    @unique @map("entity_id") @db.Uuid
  createdAt       DateTime  @default(now()) @map("created_at")
  updatedAt       DateTime  @updatedAt @map("updated_at")
  amount          String?
  deadline        DateTime?
  id              String    @id @default(uuid()) @db.Uuid
  platform        String?
  requiredSkills  String[]  @map("required_skills")
  status          String?
  taskDescription String?   @map("task_description")
  url             String?
  entity          Entity    @relation(fields: [entityId], references: [id], onDelete: Cascade)

  @@map("entity_details_bounty")
  @@schema("public")
}

model EntityDetailsHardware {
  entityId         String              @unique @map("entity_id") @db.Uuid
  createdAt        DateTime            @default(now()) @map("created_at")
  updatedAt        DateTime            @updatedAt @map("updated_at")
  availability     String?
  gpu              String?
  id               String              @id @default(uuid()) @db.Uuid
  memory           String?
  powerConsumption String?             @map("power_consumption")
  price            String?
  processor        String?
  storage          String?
  useCases         String[]            @map("use_cases")
  hardwareType     HardwareTypeEnum?   @map("hardware_type")
  manufacturer     String?
  releaseDate      DateTime?           @map("release_date")
  specifications   Json?
  datasheetUrl     String?             @map("datasheet_url")
  entity           Entity              @relation(fields: [entityId], references: [id], onDelete: Cascade)

  @@map("entity_details_hardware")
  @@schema("public")
}

model EntityDetailsNews {
  entityId        String    @unique @map("entity_id") @db.Uuid
  createdAt       DateTime  @default(now()) @map("created_at")
  updatedAt       DateTime  @updatedAt @map("updated_at")
  articleUrl      String?   @map("article_url")
  author          String?
  publicationDate DateTime? @map("publication_date")
  sourceName      String?   @map("source_name")
  summary         String?
  id              String    @id @default(uuid()) @db.Uuid
  tags            String[]
  entity          Entity    @relation(fields: [entityId], references: [id], onDelete: Cascade)

  @@map("entity_details_news")
  @@schema("public")
}

model EntityDetailsBook {
  entityId        String    @unique @map("entity_id") @db.Uuid
  createdAt       DateTime  @default(now()) @map("created_at")
  updatedAt       DateTime  @updatedAt @map("updated_at")
  isbn            String?
  pageCount       Int?      @map("page_count")
  publisher       String?
  purchaseUrl     String?   @map("purchase_url")
  summary         String?
  author          String?
  format          String?
  id              String    @id @default(uuid()) @db.Uuid
  publicationDate DateTime? @map("publication_date")
  entity          Entity    @relation(fields: [entityId], references: [id], onDelete: Cascade)

  @@map("entity_details_book")
  @@schema("public")
}

model EntityDetailsPodcast {
  entityId          String   @unique @map("entity_id") @db.Uuid
  createdAt         DateTime @default(now()) @map("created_at")
  updatedAt         DateTime @updatedAt @map("updated_at")
  frequency         String?
  applePodcastsUrl  String?  @map("apple_podcasts_url")
  averageLength     String?  @map("average_length")
  googlePodcastsUrl String?  @map("google_podcasts_url")
  host              String?
  id                String   @id @default(uuid()) @db.Uuid
  mainTopics        String[] @map("main_topics")
  spotifyUrl        String?  @map("spotify_url")
  youtubeUrl        String?  @map("youtube_url")
  entity            Entity   @relation(fields: [entityId], references: [id], onDelete: Cascade)

  @@map("entity_details_podcast")
  @@schema("public")
}

model EntityDetailsPlatform {
  entityId           String        @unique @map("entity_id") @db.Uuid
  createdAt          DateTime      @default(now()) @map("created_at")
  updatedAt          DateTime      @updatedAt @map("updated_at")
  documentationUrl   String?       @map("documentation_url")
  platformType       String?       @map("platform_type")
  communityUrl       String?       @map("community_url")
  hasFreeTier        Boolean?      @map("has_free_tier")
  hasLiveChat        Boolean?      @map("has_live_chat")
  priceRange         PriceRange?   @map("price_range")
  pricingDetails     String?       @map("pricing_details")
  pricingUrl         String?       @map("pricing_url")
  supportEmail       String?       @map("support_email")
  pricingModel       PricingModel? @map("pricing_model")
  apiAccess          Boolean?      @map("api_access")
  customizationLevel String?       @map("customization_level")
  demoAvailable      Boolean?      @map("demo_available")
  deploymentOptions  String[]      @map("deployment_options")
  hasApi             Boolean?      @map("has_api")
  id                 String        @id @default(uuid()) @db.Uuid
  mobileSupport      Boolean?      @map("mobile_support")
  openSource         Boolean?      @map("open_source")
  supportChannels    String[]      @map("support_channels")
  supportedOs        String[]      @map("supported_os")
  targetAudience     String[]      @map("target_audience")
  trialAvailable     Boolean?      @map("trial_available")
  integrations       String[]
  keyServices        String[]      @map("key_services")
  useCases           String[]      @map("use_cases")
  entity             Entity        @relation(fields: [entityId], references: [id], onDelete: Cascade)

  @@map("entity_details_platform")
  @@schema("public")
}

model EntityFeature {
  entityId   String   @map("entity_id") @db.Uuid
  featureId  String   @map("feature_id") @db.Uuid
  assignedBy String   @map("assigned_by") @db.Uuid
  createdAt  DateTime @default(now()) @map("created_at")
  id         String   @id @default(uuid()) @db.Uuid
  entity     Entity   @relation(fields: [entityId], references: [id], onDelete: Cascade)
  feature    Feature  @relation(fields: [featureId], references: [id], onDelete: Cascade)

  @@unique([entityId, featureId], map: "uq_entity_feature")
  @@map("entity_features")
  @@schema("public")
}

model AppSetting {
  key         String   @id
  value       String
  description String?
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  @@map("app_settings")
  @@schema("public")
}

model Feature {
  id             String          @id @default(uuid()) @db.Uuid
  name           String          @unique
  slug           String          @unique
  description    String?
  createdAt      DateTime        @default(now()) @map("created_at")
  updatedAt      DateTime        @updatedAt @map("updated_at")
  iconUrl        String?         @map("icon_url")
  entityFeatures EntityFeature[]

  @@map("features")
  @@schema("public")
}

model UserPreferences {
  id                  String            @id @default(uuid()) @db.Uuid
  userId              String            @unique @map("user_id") @db.Uuid
  emailNotifications  Boolean           @default(true) @map("email_notifications")
  marketingEmails     Boolean           @default(false) @map("marketing_emails")
  weeklyDigest        Boolean           @default(true) @map("weekly_digest")
  newToolAlerts       Boolean           @default(true) @map("new_tool_alerts")
  profileVisibility   ProfileVisibility @default(PUBLIC) @map("profile_visibility")
  showBookmarks       Boolean           @default(true) @map("show_bookmarks")
  showReviews         Boolean           @default(true) @map("show_reviews")
  showActivity        Boolean           @default(true) @map("show_activity")
  theme               Theme             @default(LIGHT)
  itemsPerPage        Int               @default(20) @map("items_per_page")
  defaultView         DefaultView       @default(GRID) @map("default_view")
  preferredCategories String[]          @map("preferred_categories")
  blockedCategories   String[]          @map("blocked_categories")
  contentLanguage     String            @default("en") @map("content_language")
  createdAt           DateTime          @default(now()) @map("created_at")
  updatedAt           DateTime          @updatedAt @map("updated_at")
  user                User              @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_preferences")
  @@schema("public")
}

model ToolRequest {
  id                 String              @id @default(uuid()) @db.Uuid
  userId             String              @map("user_id") @db.Uuid
  toolName           String              @map("tool_name")
  description        String
  reason             String
  categorySuggestion String?             @map("category_suggestion")
  websiteUrl         String?             @map("website_url")
  priority           ToolRequestPriority @default(MEDIUM)
  status             ToolRequestStatus   @default(PENDING)
  adminNotes         String?             @map("admin_notes")
  votes              Int                 @default(0)
  createdAt          DateTime            @default(now()) @map("created_at")
  updatedAt          DateTime            @updatedAt @map("updated_at")
  user               User                @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("tool_requests")
  @@schema("public")
}

model UserSubmittedTool {
  id               String           @id @default(uuid()) @db.Uuid
  userId           String           @map("user_id") @db.Uuid
  entityId         String           @map("entity_id") @db.Uuid
  submissionStatus SubmissionStatus @default(PENDING) @map("submission_status")
  submittedAt      DateTime         @default(now()) @map("submitted_at")
  reviewedAt       DateTime?        @map("reviewed_at")
  reviewerId       String?          @map("reviewer_id") @db.Uuid
  reviewerNotes    String?          @map("reviewer_notes")
  changesRequested String?          @map("changes_requested")
  entity           Entity           @relation(fields: [entityId], references: [id], onDelete: Cascade)
  reviewer         User?            @relation("ReviewerSubmissions", fields: [reviewerId], references: [id])
  user             User             @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, entityId])
  @@map("user_submitted_tools")
  @@schema("public")
}

model ProfileActivity {
  id          String              @id @default(uuid()) @db.Uuid
  userId      String              @map("user_id") @db.Uuid
  type        ProfileActivityType
  description String
  entityId    String?             @map("entity_id") @db.Uuid
  entityName  String?             @map("entity_name")
  entitySlug  String?             @map("entity_slug")
  createdAt   DateTime            @default(now()) @map("created_at")
  entity      Entity?             @relation(fields: [entityId], references: [id])
  user        User                @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("profile_activities")
  @@schema("public")
}

enum UserRole {
  USER
  ADMIN
  MODERATOR

  @@schema("public")
}

enum UserStatus {
  ACTIVE
  INACTIVE
  PENDING
  SUSPENDED
  DELETED

  @@schema("public")
}

enum TechnicalLevel {
  BEGINNER
  INTERMEDIATE
  ADVANCED
  EXPERT

  @@schema("public")
}

enum EntityStatus {
  PENDING
  ACTIVE
  REJECTED
  INACTIVE
  ARCHIVED
  NEEDS_REVISION

  @@schema("public")
}

enum AffiliateStatus {
  NONE
  APPLIED
  APPROVED
  REJECTED

  @@schema("public")
}

enum ReviewStatus {
  PENDING
  APPROVED
  REJECTED

  @@schema("public")
}

enum LearningCurve {
  LOW
  MEDIUM
  HIGH

  @@schema("public")
}

enum PricingModel {
  FREE
  FREEMIUM
  SUBSCRIPTION
  PAY_PER_USE
  ONE_TIME_PURCHASE
  CONTACT_SALES
  OPEN_SOURCE

  @@schema("public")
}

enum PriceRange {
  FREE
  LOW
  MEDIUM
  HIGH
  ENTERPRISE

  @@schema("public")
}

enum SkillLevel {
  BEGINNER
  INTERMEDIATE
  ADVANCED
  EXPERT

  @@schema("public")
}

enum ActionType {
  VIEW_ENTITY
  CLICK_ENTITY_LINK
  SAVE_ENTITY
  UNSAVE_ENTITY
  SUBMIT_REVIEW
  VOTE_REVIEW
  FOLLOW_TAG
  UNFOLLOW_TAG
  FOLLOW_CATEGORY
  UNFOLLOW_CATEGORY
  SEARCH
  LOGIN
  LOGOUT
  SIGNUP
  UPDATE_PROFILE
  GRANT_BADGE
  REVOKE_BADGE

  @@schema("public")
}

enum BadgeScope {
  USER
  ENTITY

  @@schema("public")
}

enum EmployeeCountRange {
  C1_10
  C11_50
  C51_200
  C201_500
  C501_1000
  C1001_5000
  C5001_PLUS

  @@schema("public")
}

enum FundingStage {
  SEED
  PRE_SEED
  SERIES_A
  SERIES_B
  SERIES_C
  SERIES_D_PLUS
  PUBLIC

  @@schema("public")
}

enum ProfileVisibility {
  PUBLIC
  PRIVATE
  FRIENDS

  @@schema("public")
}

enum Theme {
  LIGHT
  DARK
  SYSTEM

  @@schema("public")
}

enum DefaultView {
  GRID
  LIST

  @@schema("public")
}

enum ToolRequestPriority {
  LOW
  MEDIUM
  HIGH

  @@schema("public")
}

enum ToolRequestStatus {
  PENDING
  UNDER_REVIEW
  APPROVED
  REJECTED
  COMPLETED

  @@schema("public")
}

enum SubmissionStatus {
  PENDING
  UNDER_REVIEW
  APPROVED
  REJECTED
  PUBLISHED

  @@schema("public")
}

enum ProfileActivityType {
  BOOKMARK
  REVIEW
  SUBMISSION
  REQUEST
  VOTE

  @@schema("public")
}

enum HardwareTypeEnum {
  GPU
  CPU
  FPGA
  TPU
  ASIC
  NPU
  Memory
  Storage

  @@schema("public")
}

enum EmploymentTypeEnum {
  FULL_TIME
  PART_TIME
  CONTRACT
  FREELANCE
  INTERNSHIP
  TEMPORARY

  @@schema("public")
}

enum ExperienceLevelEnum {
  ENTRY
  JUNIOR
  MID
  SENIOR
  LEAD
  PRINCIPAL
  DIRECTOR

  @@schema("public")
}

enum LocationTypeEnum {
  Remote
  On_site
  Hybrid

  @@schema("public")
}

enum EventFormatEnum {
  in_person
  virtual
  hybrid

  @@schema("public")
}

model ConversationSession {
  sessionId   String   @id @map("session_id")
  userId      String   @map("user_id") @db.Uuid
  contextData Json     @map("context_data")
  createdAt   DateTime @map("created_at")
  updatedAt   DateTime @map("updated_at")
  expiresAt   DateTime @map("expires_at")

  @@map("conversation_sessions")
  @@schema("public")
}
